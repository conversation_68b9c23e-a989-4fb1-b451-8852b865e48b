/**
 * Enum representing the thirteen ranks in a standard deck of cards.
 * Ordered by traditional value: <PERSON> (1) through <PERSON> (13)
 */
public enum Rank {
    ACE("Ace", 1),
    <PERSON><PERSON><PERSON>("2", 2),
    <PERSON><PERSON><PERSON><PERSON>("3", 3),
    <PERSON><PERSON><PERSON>("4", 4),
    <PERSON><PERSON>("5", 5),
    <PERSON><PERSON>("6", 6),
    <PERSON><PERSON><PERSON>("7", 7),
    <PERSON><PERSON><PERSON>("8", 8),
    NINE("9", 9),
    T<PERSON>("10", 10),
    <PERSON><PERSON><PERSON>("Jack", 11),
    <PERSON><PERSON><PERSON>("Queen", 12),
    <PERSON><PERSON>("King", 13);
    
    private final String displayName;
    private final int value;
    
    /**
     * Constructor for Rank enum
     * @param displayName The display name of the rank
     * @param value The numeric value of the rank (1-13)
     */
    Rank(String displayName, int value) {
        this.displayName = displayName;
        this.value = value;
    }
    
    /**
     * Returns the numeric value of the rank
     * @return Integer value of the rank
     */
    public int getValue() {
        return value;
    }
    
    /**
     * Returns the display name of the rank
     * @return String representation of the rank
     */
    @Override
    public String toString() {
        return displayName;
    }
}
