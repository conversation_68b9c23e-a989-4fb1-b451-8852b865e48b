import java.util.*;

/**
 * Represents a deck of playing cards with standard operations.
 * Manages a collection of Card objects with shuffle, draw, sort, and reset functionality.
 */
public class Deck {
    private List<Card> cards;
    private Random random;
    
    /**
     * Constructor - initializes a standard 52-card deck
     */
    public Deck() {
        this.random = new Random();
        reset();
    }
    
    /**
     * Constructor with custom random seed for testing
     * @param seed Random seed for reproducible shuffling
     */
    public Deck(long seed) {
        this.random = new Random(seed);
        reset();
    }
    
    /**
     * Resets the deck to contain all 52 cards in standard order
     */
    public void reset() {
        cards = new ArrayList<>();
        
        // Create all 52 cards (13 ranks × 4 suits)
        for (Suit suit : Suit.values()) {
            for (Rank rank : Rank.values()) {
                cards.add(new Card(suit, rank));
            }
        }
    }
    
    /**
     * Shuffles the deck randomly using Collections.shuffle
     */
    public void shuffle() {
        Collections.shuffle(cards, random);
    }
    
    /**
     * Draws (removes and returns) one card from the deck
     * @return The drawn card
     * @throws IllegalStateException if the deck is empty
     */
    public Card drawCard() {
        if (isEmpty()) {
            throw new IllegalStateException("Cannot draw from an empty deck");
        }
        
        // Draw from the top of the deck (last index for efficiency)
        return cards.remove(cards.size() - 1);
    }
    
    /**
     * Draws multiple cards from the deck
     * @param count Number of cards to draw
     * @return List of drawn cards
     * @throws IllegalArgumentException if count is negative or exceeds deck size
     */
    public List<Card> drawCards(int count) {
        if (count < 0) {
            throw new IllegalArgumentException("Cannot draw negative number of cards");
        }
        if (count > cards.size()) {
            throw new IllegalArgumentException("Cannot draw " + count + " cards from deck of " + cards.size());
        }
        
        List<Card> drawnCards = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            drawnCards.add(drawCard());
        }
        return drawnCards;
    }
    
    /**
     * Sorts the deck by suit first, then by rank within each suit
     */
    public void sort() {
        Collections.sort(cards);
    }
    
    /**
     * Sorts the deck by rank first, then by suit within each rank
     */
    public void sortByRank() {
        cards.sort((card1, card2) -> {
            // First compare by rank
            int rankComparison = card1.getRank().compareTo(card2.getRank());
            if (rankComparison != 0) {
                return rankComparison;
            }
            // If ranks are equal, compare by suit
            return card1.getSuit().compareTo(card2.getSuit());
        });
    }
    
    /**
     * Prints all cards currently in the deck
     */
    public void printDeck() {
        if (isEmpty()) {
            System.out.println("Deck is empty.");
            return;
        }
        
        System.out.println("Deck (" + cards.size() + " cards):");
        for (int i = 0; i < cards.size(); i++) {
            System.out.print(cards.get(i));
            if (i < cards.size() - 1) {
                System.out.print(", ");
                // Add line break every 6 cards for readability
                if ((i + 1) % 6 == 0) {
                    System.out.println();
                }
            }
        }
        System.out.println();
    }
    
    /**
     * Returns the number of cards remaining in the deck
     * @return Number of cards in the deck
     */
    public int size() {
        return cards.size();
    }
    
    /**
     * Checks if the deck is empty
     * @return true if deck has no cards
     */
    public boolean isEmpty() {
        return cards.isEmpty();
    }
    
    /**
     * Returns a copy of the cards in the deck (for safe external access)
     * @return Unmodifiable list of cards
     */
    public List<Card> getCards() {
        return Collections.unmodifiableList(cards);
    }
    
    /**
     * Returns string representation of the deck
     * @return String showing deck size and first few cards
     */
    @Override
    public String toString() {
        if (isEmpty()) {
            return "Empty deck";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Deck (").append(cards.size()).append(" cards): ");
        
        // Show first 3 cards and last 3 cards if deck has more than 6 cards
        if (cards.size() <= 6) {
            for (int i = 0; i < cards.size(); i++) {
                sb.append(cards.get(i));
                if (i < cards.size() - 1) sb.append(", ");
            }
        } else {
            for (int i = 0; i < 3; i++) {
                sb.append(cards.get(i)).append(", ");
            }
            sb.append("..., ");
            for (int i = cards.size() - 3; i < cards.size(); i++) {
                sb.append(cards.get(i));
                if (i < cards.size() - 1) sb.append(", ");
            }
        }
        
        return sb.toString();
    }
}
