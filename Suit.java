/**
 * Enum representing the four suits in a standard deck of cards.
 * Ordered by traditional sorting: Hearts < Diamonds < Spades < Clubs
 */
public enum Suit {
    HEARTS("Hearts"),
    DIAMONDS("Diamonds"), 
    SPADES("Spades"),
    CLUBS("Clubs");
    
    private final String displayName;
    
    /**
     * Constructor for Suit enum
     * @param displayName The display name of the suit
     */
    Suit(String displayName) {
        this.displayName = displayName;
    }
    
    /**
     * Returns the display name of the suit
     * @return String representation of the suit
     */
    @Override
    public String toString() {
        return displayName;
    }
}
