/**
 * Represents a single playing card with a suit and rank.
 * Implements Comparable for sorting functionality.
 */
public class Card implements Comparable<Card> {
    private final Suit suit;
    private final Rank rank;
    
    /**
     * Constructor for Card
     * @param suit The suit of the card
     * @param rank The rank of the card
     */
    public Card(Suit suit, Rank rank) {
        if (suit == null || rank == null) {
            throw new IllegalArgumentException("Suit and rank cannot be null");
        }
        this.suit = suit;
        this.rank = rank;
    }
    
    /**
     * Gets the suit of the card
     * @return The suit of the card
     */
    public Suit getSuit() {
        return suit;
    }
    
    /**
     * Gets the rank of the card
     * @return The rank of the card
     */
    public Rank getRank() {
        return rank;
    }
    
    /**
     * Returns string representation of the card
     * @return String in format "Rank of Suit" (e.g., "Ace of Spades")
     */
    @Override
    public String toString() {
        return rank + " of " + suit;
    }
    
    /**
     * Compares cards for sorting: first by suit, then by rank
     * @param other The card to compare to
     * @return Negative if this card is less, positive if greater, 0 if equal
     */
    @Override
    public int compareTo(Card other) {
        // First compare by suit
        int suitComparison = this.suit.compareTo(other.suit);
        if (suitComparison != 0) {
            return suitComparison;
        }
        // If suits are equal, compare by rank
        return this.rank.compareTo(other.rank);
    }
    
    /**
     * Checks equality of two cards
     * @param obj The object to compare to
     * @return true if cards have same suit and rank
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Card card = (Card) obj;
        return suit == card.suit && rank == card.rank;
    }
    
    /**
     * Generates hash code for the card
     * @return Hash code based on suit and rank
     */
    @Override
    public int hashCode() {
        return suit.hashCode() * 31 + rank.hashCode();
    }
}
